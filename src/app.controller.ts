import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import { SchedulerService } from './scheduler/scheduler.service';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly schedulerService: SchedulerService,
  ) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  getHealth(): { status: string; timestamp: string } {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('scheduler/status')
  getSchedulerStatus() {
    return this.schedulerService.getSchedulerStatus();
  }

  @Get('scheduler/process-tasks')
  async manualProcessTasks() {
    await this.schedulerService.manualProcessTasks();
    return { message: 'Manual task processing triggered' };
  }
}
