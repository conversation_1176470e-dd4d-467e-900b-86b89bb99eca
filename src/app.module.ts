import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { join } from 'path';
import { existsSync } from 'fs';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { RegionsModule } from './regions/regions.module';
import { BranchesModule } from './branches/branches.module';
import { IsicSectorsModule } from './isic-sectors/isic-sectors.module';
import { CustomerCategoriesModule } from './customer-categories/customer-categories.module';
import { CustomerFeedbackCategoriesModule } from './customer-feedback-categories/customer-feedback-categories.module';
import { EmployersModule } from './employers/employers.module';
import { LeadsModule } from './leads/leads.module';
import { PermissionsModule } from './permissions/permissions.module';
import { RolesModule } from './roles/roles.module';
import { RolePermissionsModule } from './role-permissions/role-permissions.module';
import { UsersModule } from './users/users.module';
import { AnchorRelationshipsModule } from './anchor-relationships/anchor-relationships.module';
import { PurposesModule } from './purposes/purposes.module';
import { ActivitiesModule } from './activities/activities.module';
import { LoanClientsModule } from './loan-clients/loan-clients.module';
import { CustomersModule } from './customers/customers.module';
import { PurposeOfActivityModule } from './purpose-of-activity/purpose-of-activity.module';
import { PurposeCategoryModule } from './purpose-category/purpose-category.module';
import { AnchorsModule } from './anchors/anchors.module';
import { TargetsModule } from './targets/targets.module';
import { CustomerServiceHitlistModule } from './customer-service-hitlist/customer-service-hitlist.module';
import { AuthModule } from './auth/auth.module';
import { CommonModule } from './common/common.module';
import { UploadsModule } from './uploads/uploads.module';
import { LoggingModule } from './logging/logging.module';
import { LoanActivitiesModule } from './loan-activities/loan-activities.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { FinanceAnalystDashboardModule } from './finance-analyst-dashboard/finance-analyst-dashboard.module';
import { SegmentHeadDashboardModule } from './segment-head-dashboard/segment-head-dashboard.module';
import { BankerDashboardModule } from './banker-dashboard/banker-dashboard.module';
import { HolidaysModule } from './holidays/holidays.module';
import { QueueModule } from './queue/queue.module';
import { ScheduledTaskModule } from './scheduled-tasks/scheduled-task.module';
import { SchedulerModule } from './scheduler/scheduler.module';
import { ExamplesModule } from './examples/examples.module';

@Module({
  imports: [
    LoggingModule,
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        // Determine template directory based on environment
        const templateDirs = [
          join(__dirname, '..', 'templates'), // Production (dist/templates)
          join(process.cwd(), 'templates'), // Development (root/templates)
        ];

        const templateDir =
          templateDirs.find((dir) => existsSync(dir)) || templateDirs[0];

        return {
          transport: {
            host: configService.get<string>('MAIL_HOST') || 'smtp.gmail.com',
            port: parseInt(configService.get<string>('MAIL_PORT') || '587'),
            secure: false, // true for 465, false for other ports
            auth: {
              user: configService.get<string>('MAIL_USER'),
              pass: configService.get<string>('MAIL_PASS'),
            },
            tls: {
              // Do not fail on invalid certs
              rejectUnauthorized: false,
            },
            // Additional options for Gmail
            requireTLS: true,
            connectionTimeout: 60000,
            greetingTimeout: 30000,
            socketTimeout: 60000,
          },
          defaults: {
            from: configService.get<string>('MAIL_FROM'),
          },
          template: {
            dir: templateDir,
            adapter: new HandlebarsAdapter(),
            options: {
              strict: true,
            },
          },
        };
      },
      inject: [ConfigService],
    }),
    CommonModule,
    PrismaModule,
    RegionsModule,
    BranchesModule,
    IsicSectorsModule,
    CustomerCategoriesModule,
    CustomerFeedbackCategoriesModule,
    EmployersModule,
    LeadsModule,
    PermissionsModule,
    RolesModule,
    RolePermissionsModule,
    UsersModule,
    AnchorRelationshipsModule,
    PurposesModule,
    ActivitiesModule,
    LoanClientsModule,
    CustomersModule,
    PurposeOfActivityModule,
    PurposeCategoryModule,
    AnchorsModule,
    TargetsModule,
    CustomerServiceHitlistModule,
    AuthModule,
    UploadsModule,
    LoanActivitiesModule,
    DashboardModule,
    FinanceAnalystDashboardModule,
    SegmentHeadDashboardModule,
    BankerDashboardModule,
    HolidaysModule,
    QueueModule,
    ScheduledTaskModule,
    SchedulerModule,
    ExamplesModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
